/* Import Professional Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #000000;
    height: 100vh;
    overflow: hidden;
    font-weight: 500;
}

/* Navigation Bar */
.navbar {
    background: #000000;
    padding: 1.25rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-bottom: 3px solid #FF0000;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    color: #FFFFFF;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-brand i {
    margin-right: 0.75rem;
    font-size: 2rem;
    color: #FF0000;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    text-decoration: none;
    color: #FFFFFF;
    border-radius: 0;
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
}

.nav-link i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.nav-link:hover {
    background: #FF0000;
    color: #FFFFFF;
    border-color: #FF0000;
    transform: translateY(-1px);
}

.nav-link.active {
    background: #0033FF;
    color: #FFFFFF;
    border-color: #0033FF;
}

/* Main Content */
.main-content {
    height: calc(100vh - 95px);
    overflow-y: auto;
    padding: 2rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page {
    display: none;
    max-width: 1200px;
    margin: 0 auto;
}

.page.active {
    display: block;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #FFFFFF;
    border-radius: 25px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.page-header h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 3px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.page-header h1 i {
    color: #FFD700;
    margin-right: 1rem;
    animation: bounce 2s infinite;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.page-header p {
    font-size: 1.4rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    z-index: 1;
}

/* Form Styles */
.form-container {
    background: #FFFFFF;
    border: 3px solid #0033FF;
    border-radius: 0;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(0, 51, 255, 0.2);
}

.form-container h2 {
    color: #000000;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-container h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #000000;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.form-group label i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 1rem;
    border: 2px solid #000000;
    border-radius: 0;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #FFFFFF;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    pointer-events: auto;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    background: #FFFFFF;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Specific fix for className input */
#className {
    pointer-events: auto !important;
    user-select: text !important;
    -webkit-user-select: text !important;
    cursor: text !important;
}

/* Athlete Form Styling */
.athlete-form-container {
    border-color: #0033FF !important;
    box-shadow: 0 8px 24px rgba(0, 51, 255, 0.2) !important;
}

.athlete-form-container h2 {
    color: #0033FF !important;
}

.athlete-form-container h2 i {
    color: #FF0000 !important;
}

/* File Input Styling */
.file-input {
    position: relative;
    background: #FFFFFF;
    border: 2px solid #000000;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-input:hover {
    border-color: #0033FF;
    background: #F8F9FA;
}

.file-input:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
}

/* File input custom styling */
.file-input::file-selector-button {
    background: #FF0000;
    color: #FFFFFF;
    border: none;
    padding: 0.5rem 1rem;
    margin-right: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-input::file-selector-button:hover {
    background: #000000;
}

/* Select Dropdown Styling */
.form-group select {
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF0000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
    padding-right: 3rem;
}

.form-group select:hover {
    border-color: #0033FF;
}

.form-group select option {
    background: #FFFFFF;
    color: #000000;
    font-weight: 500;
    padding: 0.5rem;
}

/* Button Styles */
.btn {
    padding: 1rem 2.5rem;
    border: 3px solid transparent;
    border-radius: 0;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Inter', sans-serif;
}

.btn i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: #FF0000;
    color: #FFFFFF;
    border-color: #FF0000;
}

.btn-primary:hover {
    background: #000000;
    border-color: #000000;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
    background: #FFFFFF;
    color: #0033FF;
    border-color: #0033FF;
}

.btn-secondary:hover {
    background: #0033FF;
    color: #FFFFFF;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 51, 255, 0.3);
}

/* Classes Container */
.classes-container {
    background: #FFFFFF;
    border: 3px solid #FF0000;
    border-radius: 0;
    padding: 2.5rem;
    box-shadow: 0 8px 24px rgba(255, 0, 0, 0.2);
}

.classes-container h2 {
    color: #000000;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.classes-container h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.classes-list {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Class Card Styles */
.class-card {
    background: #FFFFFF;
    border: 2px solid #000000;
    border-radius: 0;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    border-left: 6px solid #0033FF;
}

.class-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border-left-color: #FF0000;
}

.class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.class-card-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: #000000;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.class-card-meta {
    display: flex;
    gap: 1.5rem;
    font-size: 1rem;
    color: #000000;
    margin-bottom: 1rem;
    font-weight: 600;
}

.class-card-meta span {
    display: flex;
    align-items: center;
}

.class-card-meta i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.class-card-description {
    color: #000000;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-weight: 500;
}

.class-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.athlete-count {
    background: #000000;
    color: #FFFFFF;
    padding: 0.5rem 1rem;
    border-radius: 0;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid #000000;
}

/* Coming Soon Pages */
.coming-soon {
    text-align: center;
    color: #FFFFFF;
    padding: 6rem 2rem;
    background: #000000;
    border: 3px solid #FF0000;
    border-radius: 0;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.coming-soon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 0, 0, 0.1), transparent);
    animation: sweep 3s infinite;
}

.coming-soon i {
    font-size: 5rem;
    margin-bottom: 2rem;
    color: #FF0000;
    animation: pulse 2s infinite;
}

.coming-soon h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 2px;
    animation: glitch 4s infinite;
}

.coming-soon p {
    font-size: 1.3rem;
    font-weight: 600;
    color: #0033FF;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: blink 2s infinite;
}

/* Animations */
@keyframes sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes glitch {
    0%, 90%, 100% { transform: translateX(0); }
    10% { transform: translateX(-2px); }
    20% { transform: translateX(2px); }
    30% { transform: translateX(-1px); }
    40% { transform: translateX(1px); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.7; }
}

/* Loading Indicator */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
    z-index: 1000;
}

.loading.hidden {
    display: none;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #0033FF;
    border-top: 4px solid #FF0000;
    border-radius: 0;
    animation: spin 1s linear infinite;
    margin-bottom: 2rem;
}

.loading p {
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.classes-list::-webkit-scrollbar {
    width: 12px;
}

.classes-list::-webkit-scrollbar-track {
    background: #FFFFFF;
    border: 1px solid #000000;
}

.classes-list::-webkit-scrollbar-thumb {
    background: #FF0000;
    border: 1px solid #000000;
}

.classes-list::-webkit-scrollbar-thumb:hover {
    background: #0033FF;
}

/* Empty State Styling */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #000000;
}

.empty-state i {
    font-size: 4rem;
    color: #FF0000;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000000;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
}

.toast {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: #FFFFFF;
    font-weight: 700;
    animation: slideIn 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid;
    pointer-events: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.toast-success {
    background: #000000;
    border-color: #00FF00;
    color: #00FF00;
}

.toast-error {
    background: #000000;
    border-color: #FF0000;
    color: #FF0000;
}

.toast-warning {
    background: #000000;
    border-color: #FFA500;
    color: #FFA500;
}

.toast-info {
    background: #000000;
    border-color: #0033FF;
    color: #0033FF;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Modern Leaderboard Styles */
.leaderboard-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.leaderboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #FF0000, #FFD700, #FF0000);
    animation: shimmer 2s infinite;
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.leaderboard-header h2 {
    color: #FFFFFF;
    font-size: 2rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 2px;
    display: flex;
    align-items: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.leaderboard-header h2 i {
    margin-right: 1rem;
    color: #FFD700;
    font-size: 2rem;
    animation: bounce 2s infinite;
}

.leaderboard-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    font-weight: 700;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.separator {
    color: #FFD700;
    font-weight: 800;
    font-size: 1.2rem;
}

.leaderboard-list {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.leaderboard-athlete {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
    border: none;
    border-radius: 15px;
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    min-height: 80px;
}

.leaderboard-athlete::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(180deg, #FF0000, #FF6B6B);
    border-radius: 0 10px 10px 0;
}

.leaderboard-athlete:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.leaderboard-athlete.top-3::before {
    width: 8px;
    background: linear-gradient(180deg, #FFD700, #FFA500);
}

.athlete-rank {
    font-size: 1.8rem;
    font-weight: 900;
    color: #667eea;
    min-width: 3rem;
    text-align: center;
    text-transform: uppercase;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.athlete-rank.top-3 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

.medal-icon {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 2.5rem;
    z-index: 10;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 2px solid #FFFFFF;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.medal-gold {
    color: #FFD700;
    animation: sparkle 1.5s ease-in-out infinite;
}
.medal-silver {
    color: #C0C0C0;
    animation: shine 2s ease-in-out infinite;
}
.medal-bronze {
    color: #CD7F32;
    animation: pulse 2s ease-in-out infinite;
}

.leaderboard-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #FFFFFF;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 1.4rem;
    font-weight: 900;
    text-transform: uppercase;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    position: relative;
    transition: all 0.3s ease;
}

.leaderboard-avatar::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(45deg, #FF0000, #FFD700, #0033FF, #FF0000);
    z-index: -1;
    animation: rotate 3s linear infinite;
}

.leaderboard-athlete:hover .leaderboard-avatar {
    transform: scale(1.1) rotate(5deg);
}

.athlete-info-lb {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.athlete-name-lb {
    font-size: 1.2rem;
    font-weight: 800;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1.2;
}

.athlete-nickname-lb {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    margin-top: 0.25rem;
}

.athlete-level-lb {
    font-size: 0.75rem;
    color: #FFFFFF;
    font-weight: 700;
    text-transform: uppercase;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    display: inline-block;
    width: fit-content;
    box-shadow: 0 2px 6px rgba(255, 0, 0, 0.3);
    margin-top: 0.25rem;
}

.xp-container {
    margin-top: 0.75rem;
}

.xp-label {
    font-size: 0.75rem;
    color: #667eea;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.xp-bar {
    width: 100%;
    height: 12px;
    background: rgba(102, 126, 234, 0.2);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.xp-progress {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #FF0000 100%);
    border-radius: 10px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
}

.xp-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: xp-shine 2s infinite;
}

.xp-text {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 700;
    margin-top: 0.5rem;
    text-align: center;
}

.athlete-score {
    text-align: center;
    min-width: 80px;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    border-radius: 15px;
    padding: 0.75rem 0.5rem;
    color: #FFFFFF;
    box-shadow: 0 4px 12px rgba(255, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.athlete-score::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: score-glow 3s infinite;
}

.score-value {
    font-size: 2rem;
    font-weight: 900;
    color: #FFFFFF;
    line-height: 1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.score-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
    position: relative;
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        padding: 1rem;
    }

    .nav-menu {
        margin-top: 1rem;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
        height: calc(100vh - 140px);
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .class-card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .class-card-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .coming-soon {
        padding: 3rem 1rem;
    }

    .coming-soon h2 {
        font-size: 2rem;
    }

    .athlete-form-container {
        margin-bottom: 1rem;
    }

    .file-input::file-selector-button {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
        margin-right: 0.5rem;
    }

    .form-group select {
        background-size: 0.8rem;
        padding-right: 2.5rem;
    }

    .leaderboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .leaderboard-header h2 {
        font-size: 1.5rem;
    }

    .leaderboard-stats {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .leaderboard-athlete {
        padding: 1rem;
        gap: 1rem;
        border-radius: 12px;
        margin-bottom: 0.75rem;
    }

    .athlete-rank {
        font-size: 1.4rem;
        min-width: 2.5rem;
    }

    .leaderboard-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        border-width: 2px;
    }

    .athlete-name-lb {
        font-size: 1rem;
        letter-spacing: 0.3px;
    }

    .athlete-nickname-lb {
        font-size: 0.8rem;
    }

    .athlete-level-lb {
        font-size: 0.7rem;
        padding: 0.15rem 0.5rem;
    }

    .athlete-score {
        min-width: 70px;
        padding: 0.6rem 0.4rem;
    }

    .score-value {
        font-size: 1.6rem;
    }

    .score-label {
        font-size: 0.65rem;
    }

    .medal-icon {
        font-size: 2rem;
        top: -3px;
        right: -3px;
        padding: 6px;
        width: 40px;
        height: 40px;
    }
}

/* Mobile Responsive (480px and below) */
@media (max-width: 480px) {
    .main-content {
        padding: 0.75rem;
    }

    .page-header {
        padding: 1.5rem 0.75rem;
        margin-bottom: 1.5rem;
    }

    .page-header h1 {
        font-size: 1.8rem;
        letter-spacing: 0.5px;
    }

    .leaderboard-container {
        padding: 1rem;
        border-radius: 12px;
    }

    .leaderboard-athlete {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
        padding: 1rem 0.75rem;
    }

    .athlete-rank {
        font-size: 1.2rem;
        order: 1;
    }

    .leaderboard-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.4rem;
        order: 2;
        margin: 0 auto;
    }

    .athlete-info-lb {
        order: 3;
        text-align: center;
    }

    .athlete-score {
        order: 4;
        margin: 0 auto;
        min-width: 80px;
    }

    .medal-icon {
        font-size: 1.5rem;
        top: -2px;
        right: -2px;
        padding: 4px;
        width: 32px;
        height: 32px;
    }
}

/* Fun Animations for Kids */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
}

@keyframes shine {
    0%, 100% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.1); filter: brightness(1.3); }
}

@keyframes glow {
    0%, 100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1); }
    50% { text-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 2px 2px 4px rgba(0, 0, 0, 0.1); }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes xp-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes score-glow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Run Page Styles */
.run-class-selector {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.run-class-selector h2 {
    color: #FFFFFF;
    font-size: 1.75rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.run-class-selector h2 i {
    margin-right: 0.75rem;
    color: #FFD700;
    font-size: 1.5rem;
}

.class-selector-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.class-select {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 3px solid #FFFFFF;
    border-radius: 15px;
    font-size: 1rem;
    font-weight: 600;
    background: #FFFFFF;
    color: #2D3748;
    cursor: pointer;
    transition: all 0.3s ease;
}

.class-select:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
}

.selected-class-info {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 6px solid #FF0000;
}

.class-info-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.class-details h3 {
    font-size: 1.5rem;
    font-weight: 900;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.class-meta-info {
    display: flex;
    gap: 2rem;
    font-size: 1rem;
    font-weight: 600;
    color: #667eea;
}

.class-meta-info span {
    display: flex;
    align-items: center;
}

.class-meta-info i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.session-actions {
    display: flex;
    gap: 1rem;
}

.athletes-tracking-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.tracking-header h2 {
    color: #2D3748;
    font-size: 1.75rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
}

.tracking-header h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
    animation: pulse 2s infinite;
}

.session-timers {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.session-timer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 1.2rem;
    font-weight: 900;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    color: #FFFFFF;
    padding: 0.75rem 1rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
    min-width: 80px;
}

.session-timer i {
    color: #FFD700;
    animation: tick 1s infinite;
    font-size: 1rem;
}

.timer-label {
    font-size: 0.7rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Class Timer Styles */
.class-timer {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
    border: 3px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    min-width: 300px;
}

.class-timer-display {
    text-align: center;
    margin-bottom: 1rem;
}

.class-timer .timer-time {
    font-size: 3rem;
    font-weight: 900;
    color: #FFFFFF;
    font-family: 'Courier New', monospace;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
}

.class-timer .timer-progress-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.class-timer .timer-progress-bar {
    flex: 1;
    height: 12px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.5);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.class-timer .timer-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10B981, #059669);
    transition: width 0.3s ease, background 0.3s ease;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
}

.class-timer .timer-duration {
    font-size: 1rem;
    font-weight: 700;
    color: #FFFFFF;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.class-timer-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    justify-content: center;
}

.timer-select {
    padding: 0.5rem 0.75rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.9);
    color: #2D3748;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timer-select:focus {
    outline: none;
    border-color: #FFFFFF;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.timer-select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.class-timer-btn {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
}

.class-timer-btn i {
    margin-right: 0.5rem;
}

.class-timer-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.3);
    border-color: #FFFFFF;
}

.class-timer-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.class-timer-btn.start-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #10B981, #059669);
    border-color: #10B981;
}

.class-timer-btn.stop-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #EF4444, #DC2626);
    border-color: #EF4444;
}

.class-timer-btn.reset-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    border-color: #8B5CF6;
}

/* Timer pulse animation */
@keyframes timer-pulse {
    0%, 100% {
        transform: scale(1);
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.05);
        text-shadow: 3px 3px 12px rgba(255, 215, 0, 0.6);
    }
}

.athletes-tracking-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.athlete-tracking-card {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
    position: relative;
}

.athlete-tracking-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.athlete-tracking-card.completed {
    border-left-color: #10B981;
    background: linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 100%);
    opacity: 0.8;
}

.athlete-tracking-card.completed .athlete-tracking-avatar {
    border-color: #10B981;
}

.athlete-card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.athlete-current-score {
    margin-left: auto;
}

.score-display {
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #FFFFFF;
    padding: 0.5rem;
    border-radius: 10px;
    min-width: 60px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.score-display .score-value {
    font-size: 1.5rem;
    font-weight: 900;
    line-height: 1;
    display: block;
}

.score-display .score-label {
    font-size: 0.6rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.athlete-tracking-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 1.4rem;
    font-weight: 900;
    text-transform: uppercase;
    flex-shrink: 0;
}

.athlete-tracking-info h4 {
    font-size: 1.2rem;
    font-weight: 800;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.athlete-tracking-info .nickname {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
    font-style: italic;
    margin-bottom: 0.5rem;
}

.athlete-tracking-info .level {
    font-size: 0.75rem;
    color: #FFFFFF;
    font-weight: 700;
    text-transform: uppercase;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    display: inline-block;
}



/* Scoring Section */
.scoring-section {
    background: linear-gradient(135deg, #FFF8E1, #FFF3C4);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #FFD700;
}

.scoring-label {
    font-size: 0.9rem;
    font-weight: 700;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
    text-align: center;
}

.scoring-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.score-btn {
    padding: 0.75rem 0.5rem;
    border: 2px solid;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.score-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.score-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.plus-btn {
    background: linear-gradient(135deg, #10B981, #059669);
    color: #FFFFFF;
    border-color: #10B981;
}

.mystery-btn {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: #FFFFFF;
    border-color: #8B5CF6;
    position: relative;
}

.mystery-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: mystery-shine 2s infinite;
}

.miss-btn {
    background: linear-gradient(135deg, #FF0000, #DC2626);
    color: #FFFFFF;
    border-color: #FF0000;
}

/* Score Animation */
.score-animation {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    font-size: 1.2rem;
    font-weight: 900;
    color: #10B981;
    pointer-events: none;
    animation: score-popup 1.5s ease-out forwards;
    z-index: 10;
}

@keyframes score-popup {
    0% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-70%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-90%) scale(0.8);
    }
}

@keyframes mystery-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.notes-section {
    margin-bottom: 1.5rem;
}

.notes-section label {
    display: block;
    font-size: 0.9rem;
    font-weight: 700;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.notes-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #E2E8F0;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    resize: vertical;
    min-height: 80px;
    transition: all 0.3s ease;
    background: #FFFFFF;
    font-family: 'Inter', sans-serif;
}

.notes-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-athlete-btn {
    width: 100%;
    padding: 0.75rem;
    background: linear-gradient(135deg, #10B981, #059669);
    color: #FFFFFF;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.submit-athlete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.submit-athlete-btn:disabled {
    background: #9CA3AF;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.submit-athlete-btn i {
    margin-right: 0.5rem;
}

.session-summary {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 2px solid rgba(102, 126, 234, 0.2);
}

/* Scrollbar Styling */
.leaderboard-list::-webkit-scrollbar,
.athletes-tracking-grid::-webkit-scrollbar {
    width: 8px;
}

.leaderboard-list::-webkit-scrollbar-track,
.athletes-tracking-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.leaderboard-list::-webkit-scrollbar-thumb,
.athletes-tracking-grid::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
}

.leaderboard-list::-webkit-scrollbar-thumb:hover,
.athletes-tracking-grid::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
}

/* Run Page Responsive Styles */
@media (max-width: 768px) {
    .run-class-selector {
        padding: 1.5rem;
    }

    .class-selector-container {
        flex-direction: column;
        gap: 1rem;
    }

    .class-info-card {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .class-meta-info {
        flex-direction: column;
        gap: 0.75rem;
    }

    .session-actions {
        flex-direction: column;
        width: 100%;
    }

    .athletes-tracking-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .athlete-tracking-card {
        padding: 1.25rem;
    }

    .tracking-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .session-timers {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .class-timer {
        min-width: auto;
        width: 100%;
    }

    .class-timer .timer-time {
        font-size: 2.5rem;
    }

    .class-timer-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .class-timer-btn {
        flex: 1;
        min-width: 80px;
        justify-content: center;
    }

    .timer-select {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .session-summary {
        flex-direction: column;
        gap: 1rem;
    }

    .scoring-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .score-btn {
        padding: 1rem;
        font-size: 1rem;
    }
}

/* Run Page Tablet Styles */
@media (min-width: 481px) and (max-width: 767px) {
    .athletes-tracking-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .class-info-card {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
}

/* Additional animations for Run page */
@keyframes tick {
    0%, 50% { transform: scale(1); }
    25% { transform: scale(1.1); }
}

/* Settings Page Styles */
.settings-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.settings-menu {
    background: #FFFFFF;
    border: 2px solid #E9ECEF;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-item {
    border-bottom: 1px solid #E9ECEF;
    transition: all 0.2s ease;
}

.settings-item:last-child {
    border-bottom: none;
}

.settings-item-header {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #FFFFFF;
}

.settings-item-header:hover {
    background: #F8F9FA;
}

.settings-item.active .settings-item-header {
    background: #F8F9FA;
    border-bottom: 1px solid #E9ECEF;
}

.settings-item-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.settings-item-icon i {
    font-size: 1.5rem;
    color: #FFFFFF;
}

.scoring-icon {
    background: linear-gradient(135deg, #FFD700, #FFA500);
}

.athletes-icon {
    background: linear-gradient(135deg, #32CD32, #228B22);
}

.timer-icon {
    background: linear-gradient(135deg, #FF6347, #DC143C);
}

.point-icon {
    background: linear-gradient(135deg, #00CED1, #008B8B);
}

.theme-icon {
    background: linear-gradient(135deg, #9370DB, #8A2BE2);
}

.achievement-icon {
    background: linear-gradient(135deg, #FFD700, #FF8C00);
}

.settings-item-content {
    flex: 1;
}

.settings-item-content h3 {
    color: #000000;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    letter-spacing: 0.5px;
}

.settings-item-content p {
    color: #666666;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

.settings-item-arrow {
    margin-left: 1rem;
    transition: transform 0.2s ease;
}

.settings-item-arrow i {
    color: #999999;
    font-size: 1rem;
}

.settings-item.active .settings-item-arrow {
    transform: rotate(90deg);
}

.settings-item-panel {
    display: none;
    padding: 0 1.5rem 1.5rem 1.5rem;
    background: #F8F9FA;
    border-top: 1px solid #E9ECEF;
}

.settings-item.active .settings-item-panel {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 1rem;
}

.setting-card {
    background: #FFFFFF;
    border: 2px solid #E9ECEF;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.setting-card:hover {
    border-color: #0033FF;
    box-shadow: 0 4px 12px rgba(0, 51, 255, 0.1);
}

.setting-card h4 {
    color: #000000;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.setting-card h4 i {
    margin-right: 0.5rem;
    color: #0033FF;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.feature-list li {
    padding: 0.5rem 0;
    color: #666666;
    font-weight: 500;
}

.feature-list li:before {
    content: "•";
    color: #0033FF;
    font-weight: bold;
    margin-right: 0.5rem;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    color: #000000;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.setting-group input[type="number"],
.setting-group input[type="color"],
.setting-group input[type="range"],
.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #E9ECEF;
    border-radius: 6px;
    background: #FFFFFF;
    color: #000000;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.setting-group input[type="number"]:focus,
.setting-group input[type="color"]:focus,
.setting-group input[type="range"]:focus,
.setting-group select:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
}

.setting-group input[type="number"]:disabled,
.setting-group select:disabled {
    background: #F8F9FA;
    color: #6C757D;
    cursor: not-allowed;
    border-color: #E9ECEF;
}

.setting-group input[type="color"] {
    height: 50px;
    padding: 0.25rem;
    cursor: pointer;
}

.setting-group input[type="range"] {
    padding: 0;
    height: 6px;
    background: #E9ECEF;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.setting-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #0033FF;
    cursor: pointer;
    border: 2px solid #FFFFFF;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.setting-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 0.75rem;
    accent-color: #FF0000;
}

.setting-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.setting-description {
    display: block;
    color: #666666;
    font-size: 0.8rem;
    font-weight: 500;
    font-style: italic;
    margin-top: 0.25rem;
}

.settings-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #E9ECEF;
}

.settings-actions .btn {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 150px;
}

/* Settings Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        padding: 1rem;
    }

    .settings-item-header {
        padding: 1rem;
    }

    .settings-item-icon {
        width: 40px;
        height: 40px;
        margin-right: 0.75rem;
    }

    .settings-item-icon i {
        font-size: 1.2rem;
    }

    .settings-item-content h3 {
        font-size: 1.1rem;
    }

    .settings-item-content p {
        font-size: 0.8rem;
    }

    .settings-item-panel {
        padding: 0 1rem 1rem 1rem;
    }

    .setting-card {
        padding: 1rem;
    }

    .settings-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .settings-actions .btn {
        width: 100%;
        max-width: 300px;
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}



/* Achievement Notification Styles */
.achievement-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    border: 3px solid #FFFFFF;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.4);
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.5s ease;
    max-width: 350px;
    animation: achievementGlow 2s ease-in-out infinite alternate;
}

.achievement-notification.show {
    transform: translateX(0);
    opacity: 1;
}

@keyframes achievementGlow {
    from {
        box-shadow: 0 8px 32px rgba(255, 215, 0, 0.4);
    }
    to {
        box-shadow: 0 8px 32px rgba(255, 215, 0, 0.8);
    }
}

.achievement-notification-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.achievement-notification-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 2px solid #FFFFFF;
}

.achievement-notification-icon i {
    font-size: 1.8rem;
    color: #FFFFFF;
}

.achievement-notification-icon.common {
    background: linear-gradient(135deg, #6C757D, #495057);
}

.achievement-notification-icon.uncommon {
    background: linear-gradient(135deg, #28A745, #20C997);
}

.achievement-notification-icon.rare {
    background: linear-gradient(135deg, #007BFF, #0056B3);
}

.achievement-notification-icon.legendary {
    background: linear-gradient(135deg, #FFD700, #FF8C00);
}

.achievement-notification-text {
    flex: 1;
    color: #FFFFFF;
}

.achievement-notification-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.achievement-notification-name {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.achievement-notification-athlete {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.achievement-notification-reward {
    font-size: 0.8rem;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

/* Achievement Stats in Settings */
.achievement-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #F8F9FA;
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 800;
    color: #FF0000;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Athlete Profile Page Styles */
.profile-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    color: #FFFFFF;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #FFFFFF;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.profile-header h1 {
    color: #FFFFFF;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 800;
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem 2rem 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* Character Card */
.character-card {
    background: linear-gradient(135deg, #FFFFFF, #F8F9FA);
    border: 3px solid #FF0000;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(255, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.character-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #FF0000, #0033FF, #FF0000);
}

.character-avatar-section {
    position: relative;
    flex-shrink: 0;
}

.character-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: 800;
    color: #FFFFFF;
    border: 4px solid #FFFFFF;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    position: relative;
}

.character-level-badge {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    color: #FFFFFF;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    border: 2px solid #FFFFFF;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.character-info {
    flex: 1;
}

.character-name {
    font-size: 2rem;
    font-weight: 800;
    color: #000000;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.character-nickname {
    font-size: 1.2rem;
    color: #666666;
    font-style: italic;
    margin-bottom: 0.5rem;
}

.character-title {
    font-size: 1rem;
    color: #FF0000;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1.5rem;
}

.xp-section {
    background: rgba(0, 51, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    border: 2px solid rgba(0, 51, 255, 0.2);
}

.xp-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #0033FF;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.xp-bar {
    width: 100%;
    height: 12px;
    background: #E9ECEF;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 0.5rem;
    border: 1px solid #0033FF;
}

.xp-progress {
    height: 100%;
    background: linear-gradient(90deg, #0033FF, #00BFFF);
    transition: width 0.3s ease;
    border-radius: 6px;
}

.xp-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: #0033FF;
    text-align: center;
}

/* Stats Dashboard */
.stats-dashboard {
    background: #FFFFFF;
    border: 2px solid #E9ECEF;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-dashboard h3 {
    color: #000000;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-card {
    background: #F8F9FA;
    border: 2px solid #E9ECEF;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.total-points {
    border-color: #FFD700;
}

.stat-card.sessions-attended {
    border-color: #28A745;
}

.stat-card.best-score {
    border-color: #FF0000;
}

.stat-card.achievements-unlocked {
    border-color: #0033FF;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.total-points .stat-icon {
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    color: #FFFFFF;
}

.sessions-attended .stat-icon {
    background: linear-gradient(135deg, #28A745, #20C997);
    color: #FFFFFF;
}

.best-score .stat-icon {
    background: linear-gradient(135deg, #FF0000, #DC143C);
    color: #FFFFFF;
}

.achievements-unlocked .stat-icon {
    background: linear-gradient(135deg, #0033FF, #007BFF);
    color: #FFFFFF;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 800;
    color: #000000;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-card .stat-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Profile Achievements */
.profile-achievements {
    background: #FFFFFF;
    border: 2px solid #E9ECEF;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-achievements h3 {
    color: #000000;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.achievements-showcase {
    display: grid;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.achievement-badge {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 2px solid #E9ECEF;
    border-radius: 8px;
    background: #F8F9FA;
    transition: all 0.2s ease;
}

.achievement-badge.unlocked {
    border-color: #28A745;
    background: linear-gradient(135deg, #F8FFF9, #FFFFFF);
}

.achievement-badge.locked {
    opacity: 0.6;
    filter: grayscale(50%);
}

.achievement-badge-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.achievement-badge-icon i {
    font-size: 1.5rem;
    color: #FFFFFF;
}

.achievement-badge-icon.common {
    background: linear-gradient(135deg, #6C757D, #495057);
}

.achievement-badge-icon.uncommon {
    background: linear-gradient(135deg, #28A745, #20C997);
}

.achievement-badge-icon.rare {
    background: linear-gradient(135deg, #007BFF, #0056B3);
}

.achievement-badge-icon.legendary {
    background: linear-gradient(135deg, #FFD700, #FF8C00);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
}

.achievement-badge-info {
    flex: 1;
}

.achievement-badge-info h4 {
    color: #000000;
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.achievement-badge-info p {
    color: #666666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.achievement-progress-mini {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-bar-mini {
    flex: 1;
    height: 6px;
    background: #E9ECEF;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill-mini {
    height: 100%;
    background: linear-gradient(90deg, #0033FF, #00BFFF);
    transition: width 0.3s ease;
}

.progress-text-mini {
    font-size: 0.8rem;
    font-weight: 600;
    color: #666666;
}

.achievement-unlocked-badge {
    color: #28A745;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Recent Activity */
.recent-activity {
    background: #FFFFFF;
    border: 2px solid #E9ECEF;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recent-activity h3 {
    color: #000000;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.activity-list {
    display: grid;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #E9ECEF;
    border-radius: 8px;
    background: #F8F9FA;
    transition: all 0.2s ease;
}

.activity-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0033FF, #007BFF);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    flex-shrink: 0;
}

.activity-info {
    flex: 1;
}

.activity-title {
    font-size: 1rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 0.25rem;
}

.activity-details {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.25rem;
}

.activity-score {
    color: #28A745;
    font-weight: 600;
    font-size: 0.9rem;
}

.activity-notes {
    color: #666666;
    font-style: italic;
    font-size: 0.9rem;
}

.activity-time {
    color: #999999;
    font-size: 0.8rem;
    font-weight: 500;
}

.no-activity {
    text-align: center;
    padding: 2rem;
    color: #666666;
}

.no-activity i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Roster Modal Styles */
.roster-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 2rem;
}

.roster-modal {
    background: #FFFFFF;
    border-radius: 16px;
    max-width: 800px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
}

.roster-modal-header {
    background: linear-gradient(135deg, #FF0000, #0033FF);
    color: #FFFFFF;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.roster-modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-modal-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: #FFFFFF;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.close-modal-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.roster-modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

.athletes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.athlete-card {
    background: #F8F9FA;
    border: 2px solid #E9ECEF;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.athlete-card:hover {
    border-color: #0033FF;
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 51, 255, 0.2);
}

.athlete-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 800;
    color: #FFFFFF;
    margin: 0 auto 1rem auto;
    border: 3px solid #FFFFFF;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.athlete-info h4 {
    color: #000000;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.athlete-nickname {
    color: #666666;
    font-style: italic;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.athlete-level {
    color: #0033FF;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.view-profile-btn {
    background: linear-gradient(135deg, #0033FF, #007BFF);
    color: #FFFFFF;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: auto;
}

/* Profile Responsive Design */
@media (max-width: 768px) {
    .profile-container {
        grid-template-columns: 1fr;
        padding: 0 1rem 1rem 1rem;
        gap: 1rem;
    }

    .character-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .character-avatar {
        width: 100px;
        height: 100px;
        font-size: 2rem;
    }

    .character-name {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .athletes-grid {
        grid-template-columns: 1fr;
    }

    .roster-modal {
        margin: 1rem;
        max-height: 90vh;
    }

    .roster-modal-body {
        padding: 1rem;
    }
}

/* Athlete Management Styles */
.athlete-management-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.athletes-management-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #E9ECEF;
    border-radius: 8px;
    background: #F8F9FA;
}

.athlete-management-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #E9ECEF;
    background: #FFFFFF;
    transition: all 0.2s ease;
}

.athlete-management-item:last-child {
    border-bottom: none;
}

.athlete-management-item:hover {
    background: #F8F9FA;
}

.athlete-checkbox {
    flex-shrink: 0;
}

.athlete-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.athlete-avatar-small {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-weight: 700;
    font-size: 1.2rem;
    flex-shrink: 0;
    overflow: hidden;
}

.athlete-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.athlete-details {
    flex: 1;
}

.athlete-details h5 {
    color: #000000;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.athlete-nickname {
    color: #666666;
    font-style: italic;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    display: block;
}

.athlete-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #999999;
}

.athlete-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.no-athletes,
.error-message {
    text-align: center;
    padding: 2rem;
    color: #666666;
}

.no-athletes i,
.error-message i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Athlete Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 2rem;
}

.athlete-modal {
    background: #FFFFFF;
    border-radius: 16px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
}

.athlete-modal-header {
    background: linear-gradient(135deg, #FF0000, #0033FF);
    color: #FFFFFF;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.athlete-modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
}

.athlete-modal-body {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #000000;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #E9ECEF;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
}

.avatar-upload-group {
    grid-column: 1 / -1;
}

.avatar-upload-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-weight: 700;
    font-size: 1.8rem;
    flex-shrink: 0;
    overflow: hidden;
    border: 3px solid #E9ECEF;
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.avatar-upload-controls {
    flex: 1;
}

.file-input {
    margin-bottom: 0.5rem;
}

.athlete-modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #E9ECEF;
}

/* Athlete Modal Responsive */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 1rem;
    }

    .athlete-modal {
        max-height: 95vh;
    }

    .athlete-modal-body {
        padding: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .athlete-modal-actions {
        flex-direction: column;
    }

    .athlete-management-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .athlete-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
