/* Import Professional Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #000000;
    height: 100vh;
    overflow: hidden;
    font-weight: 500;
}

/* Navigation Bar */
.navbar {
    background: #000000;
    padding: 1.25rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-bottom: 3px solid #FF0000;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    color: #FFFFFF;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-brand i {
    margin-right: 0.75rem;
    font-size: 2rem;
    color: #FF0000;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    text-decoration: none;
    color: #FFFFFF;
    border-radius: 0;
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
}

.nav-link i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.nav-link:hover {
    background: #FF0000;
    color: #FFFFFF;
    border-color: #FF0000;
    transform: translateY(-1px);
}

.nav-link.active {
    background: #0033FF;
    color: #FFFFFF;
    border-color: #0033FF;
}

/* Main Content */
.main-content {
    height: calc(100vh - 95px);
    overflow-y: auto;
    padding: 2rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page {
    display: none;
    max-width: 1200px;
    margin: 0 auto;
}

.page.active {
    display: block;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #FFFFFF;
    border-radius: 25px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.page-header h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 3px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.page-header h1 i {
    color: #FFD700;
    margin-right: 1rem;
    animation: bounce 2s infinite;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.page-header p {
    font-size: 1.4rem;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    z-index: 1;
}

/* Form Styles */
.form-container {
    background: #FFFFFF;
    border: 3px solid #0033FF;
    border-radius: 0;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(0, 51, 255, 0.2);
}

.form-container h2 {
    color: #000000;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-container h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #000000;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.form-group label i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 1rem;
    border: 2px solid #000000;
    border-radius: 0;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #FFFFFF;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    pointer-events: auto;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    background: #FFFFFF;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Specific fix for className input */
#className {
    pointer-events: auto !important;
    user-select: text !important;
    -webkit-user-select: text !important;
    cursor: text !important;
}

/* Athlete Form Styling */
.athlete-form-container {
    border-color: #0033FF !important;
    box-shadow: 0 8px 24px rgba(0, 51, 255, 0.2) !important;
}

.athlete-form-container h2 {
    color: #0033FF !important;
}

.athlete-form-container h2 i {
    color: #FF0000 !important;
}

/* File Input Styling */
.file-input {
    position: relative;
    background: #FFFFFF;
    border: 2px solid #000000;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-input:hover {
    border-color: #0033FF;
    background: #F8F9FA;
}

.file-input:focus {
    outline: none;
    border-color: #0033FF;
    box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
}

/* File input custom styling */
.file-input::file-selector-button {
    background: #FF0000;
    color: #FFFFFF;
    border: none;
    padding: 0.5rem 1rem;
    margin-right: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-input::file-selector-button:hover {
    background: #000000;
}

/* Select Dropdown Styling */
.form-group select {
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FF0000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
    padding-right: 3rem;
}

.form-group select:hover {
    border-color: #0033FF;
}

.form-group select option {
    background: #FFFFFF;
    color: #000000;
    font-weight: 500;
    padding: 0.5rem;
}

/* Button Styles */
.btn {
    padding: 1rem 2.5rem;
    border: 3px solid transparent;
    border-radius: 0;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Inter', sans-serif;
}

.btn i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: #FF0000;
    color: #FFFFFF;
    border-color: #FF0000;
}

.btn-primary:hover {
    background: #000000;
    border-color: #000000;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
    background: #FFFFFF;
    color: #0033FF;
    border-color: #0033FF;
}

.btn-secondary:hover {
    background: #0033FF;
    color: #FFFFFF;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 51, 255, 0.3);
}

/* Classes Container */
.classes-container {
    background: #FFFFFF;
    border: 3px solid #FF0000;
    border-radius: 0;
    padding: 2.5rem;
    box-shadow: 0 8px 24px rgba(255, 0, 0, 0.2);
}

.classes-container h2 {
    color: #000000;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.classes-container h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.classes-list {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Class Card Styles */
.class-card {
    background: #FFFFFF;
    border: 2px solid #000000;
    border-radius: 0;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    border-left: 6px solid #0033FF;
}

.class-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border-left-color: #FF0000;
}

.class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.class-card-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: #000000;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.class-card-meta {
    display: flex;
    gap: 1.5rem;
    font-size: 1rem;
    color: #000000;
    margin-bottom: 1rem;
    font-weight: 600;
}

.class-card-meta span {
    display: flex;
    align-items: center;
}

.class-card-meta i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.class-card-description {
    color: #000000;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-weight: 500;
}

.class-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.athlete-count {
    background: #000000;
    color: #FFFFFF;
    padding: 0.5rem 1rem;
    border-radius: 0;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid #000000;
}

/* Coming Soon Pages */
.coming-soon {
    text-align: center;
    color: #FFFFFF;
    padding: 6rem 2rem;
    background: #000000;
    border: 3px solid #FF0000;
    border-radius: 0;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.coming-soon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 0, 0, 0.1), transparent);
    animation: sweep 3s infinite;
}

.coming-soon i {
    font-size: 5rem;
    margin-bottom: 2rem;
    color: #FF0000;
    animation: pulse 2s infinite;
}

.coming-soon h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 2px;
    animation: glitch 4s infinite;
}

.coming-soon p {
    font-size: 1.3rem;
    font-weight: 600;
    color: #0033FF;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: blink 2s infinite;
}

/* Animations */
@keyframes sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes glitch {
    0%, 90%, 100% { transform: translateX(0); }
    10% { transform: translateX(-2px); }
    20% { transform: translateX(2px); }
    30% { transform: translateX(-1px); }
    40% { transform: translateX(1px); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.7; }
}

/* Loading Indicator */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
    z-index: 1000;
}

.loading.hidden {
    display: none;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #0033FF;
    border-top: 4px solid #FF0000;
    border-radius: 0;
    animation: spin 1s linear infinite;
    margin-bottom: 2rem;
}

.loading p {
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.classes-list::-webkit-scrollbar {
    width: 12px;
}

.classes-list::-webkit-scrollbar-track {
    background: #FFFFFF;
    border: 1px solid #000000;
}

.classes-list::-webkit-scrollbar-thumb {
    background: #FF0000;
    border: 1px solid #000000;
}

.classes-list::-webkit-scrollbar-thumb:hover {
    background: #0033FF;
}

/* Empty State Styling */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #000000;
}

.empty-state i {
    font-size: 4rem;
    color: #FF0000;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000000;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 0;
    color: #FFFFFF;
    font-weight: 700;
    z-index: 1001;
    animation: slideIn 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid;
}

.toast-success {
    background: #000000;
    border-color: #00FF00;
    color: #00FF00;
}

.toast-error {
    background: #000000;
    border-color: #FF0000;
    color: #FF0000;
}

.toast-info {
    background: #000000;
    border-color: #0033FF;
    color: #0033FF;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Modern Leaderboard Styles */
.leaderboard-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.leaderboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #FF0000, #FFD700, #FF0000);
    animation: shimmer 2s infinite;
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.leaderboard-header h2 {
    color: #FFFFFF;
    font-size: 2rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 2px;
    display: flex;
    align-items: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.leaderboard-header h2 i {
    margin-right: 1rem;
    color: #FFD700;
    font-size: 2rem;
    animation: bounce 2s infinite;
}

.leaderboard-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    font-weight: 700;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.separator {
    color: #FFD700;
    font-weight: 800;
    font-size: 1.2rem;
}

.leaderboard-list {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.leaderboard-athlete {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
    border: none;
    border-radius: 15px;
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    min-height: 80px;
}

.leaderboard-athlete::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(180deg, #FF0000, #FF6B6B);
    border-radius: 0 10px 10px 0;
}

.leaderboard-athlete:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.leaderboard-athlete.top-3::before {
    width: 8px;
    background: linear-gradient(180deg, #FFD700, #FFA500);
}

.athlete-rank {
    font-size: 1.8rem;
    font-weight: 900;
    color: #667eea;
    min-width: 3rem;
    text-align: center;
    text-transform: uppercase;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.athlete-rank.top-3 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

.medal-icon {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 3.5rem;
    z-index: 10;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.4));
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.medal-gold {
    color: #FFD700;
    animation: sparkle 1.5s ease-in-out infinite;
}
.medal-silver {
    color: #C0C0C0;
    animation: shine 2s ease-in-out infinite;
}
.medal-bronze {
    color: #CD7F32;
    animation: pulse 2s ease-in-out infinite;
}

.leaderboard-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #FFFFFF;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 1.4rem;
    font-weight: 900;
    text-transform: uppercase;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    position: relative;
    transition: all 0.3s ease;
}

.leaderboard-avatar::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(45deg, #FF0000, #FFD700, #0033FF, #FF0000);
    z-index: -1;
    animation: rotate 3s linear infinite;
}

.leaderboard-athlete:hover .leaderboard-avatar {
    transform: scale(1.1) rotate(5deg);
}

.athlete-info-lb {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.athlete-name-lb {
    font-size: 1.2rem;
    font-weight: 800;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1.2;
}

.athlete-nickname-lb {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    margin-top: 0.25rem;
}

.athlete-level-lb {
    font-size: 0.75rem;
    color: #FFFFFF;
    font-weight: 700;
    text-transform: uppercase;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    display: inline-block;
    width: fit-content;
    box-shadow: 0 2px 6px rgba(255, 0, 0, 0.3);
    margin-top: 0.25rem;
}

.xp-container {
    margin-top: 0.75rem;
}

.xp-label {
    font-size: 0.75rem;
    color: #667eea;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.xp-bar {
    width: 100%;
    height: 12px;
    background: rgba(102, 126, 234, 0.2);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.xp-progress {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #FF0000 100%);
    border-radius: 10px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
}

.xp-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: xp-shine 2s infinite;
}

.xp-text {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 700;
    margin-top: 0.5rem;
    text-align: center;
}

.athlete-score {
    text-align: center;
    min-width: 80px;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    border-radius: 15px;
    padding: 0.75rem 0.5rem;
    color: #FFFFFF;
    box-shadow: 0 4px 12px rgba(255, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.athlete-score::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: score-glow 3s infinite;
}

.score-value {
    font-size: 2rem;
    font-weight: 900;
    color: #FFFFFF;
    line-height: 1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.score-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
    position: relative;
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        padding: 1rem;
    }

    .nav-menu {
        margin-top: 1rem;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
        height: calc(100vh - 140px);
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .class-card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .class-card-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .coming-soon {
        padding: 3rem 1rem;
    }

    .coming-soon h2 {
        font-size: 2rem;
    }

    .athlete-form-container {
        margin-bottom: 1rem;
    }

    .file-input::file-selector-button {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
        margin-right: 0.5rem;
    }

    .form-group select {
        background-size: 0.8rem;
        padding-right: 2.5rem;
    }

    .leaderboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .leaderboard-header h2 {
        font-size: 1.5rem;
    }

    .leaderboard-stats {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .leaderboard-athlete {
        padding: 1rem;
        gap: 1rem;
        border-radius: 12px;
        margin-bottom: 0.75rem;
    }

    .athlete-rank {
        font-size: 1.4rem;
        min-width: 2.5rem;
    }

    .leaderboard-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        border-width: 2px;
    }

    .athlete-name-lb {
        font-size: 1rem;
        letter-spacing: 0.3px;
    }

    .athlete-nickname-lb {
        font-size: 0.8rem;
    }

    .athlete-level-lb {
        font-size: 0.7rem;
        padding: 0.15rem 0.5rem;
    }

    .athlete-score {
        min-width: 70px;
        padding: 0.6rem 0.4rem;
    }

    .score-value {
        font-size: 1.6rem;
    }

    .score-label {
        font-size: 0.65rem;
    }

    .medal-icon {
        font-size: 2.5rem;
        top: 5px;
        right: 8px;
        padding: 4px;
    }
}

/* Mobile Responsive (480px and below) */
@media (max-width: 480px) {
    .main-content {
        padding: 0.75rem;
    }

    .page-header {
        padding: 1.5rem 0.75rem;
        margin-bottom: 1.5rem;
    }

    .page-header h1 {
        font-size: 1.8rem;
        letter-spacing: 0.5px;
    }

    .leaderboard-container {
        padding: 1rem;
        border-radius: 12px;
    }

    .leaderboard-athlete {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
        padding: 1rem 0.75rem;
    }

    .athlete-rank {
        font-size: 1.2rem;
        order: 1;
    }

    .leaderboard-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.4rem;
        order: 2;
        margin: 0 auto;
    }

    .athlete-info-lb {
        order: 3;
        text-align: center;
    }

    .athlete-score {
        order: 4;
        margin: 0 auto;
        min-width: 80px;
    }

    .medal-icon {
        font-size: 2rem;
        top: 2px;
        right: 5px;
        padding: 3px;
    }
}

/* Fun Animations for Kids */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
}

@keyframes shine {
    0%, 100% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.1); filter: brightness(1.3); }
}

@keyframes glow {
    0%, 100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1); }
    50% { text-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 2px 2px 4px rgba(0, 0, 0, 0.1); }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes xp-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes score-glow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Run Page Styles */
.run-class-selector {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.run-class-selector h2 {
    color: #FFFFFF;
    font-size: 1.75rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.run-class-selector h2 i {
    margin-right: 0.75rem;
    color: #FFD700;
    font-size: 1.5rem;
}

.class-selector-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.class-select {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 3px solid #FFFFFF;
    border-radius: 15px;
    font-size: 1rem;
    font-weight: 600;
    background: #FFFFFF;
    color: #2D3748;
    cursor: pointer;
    transition: all 0.3s ease;
}

.class-select:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
}

.selected-class-info {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-left: 6px solid #FF0000;
}

.class-info-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.class-details h3 {
    font-size: 1.5rem;
    font-weight: 900;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.class-meta-info {
    display: flex;
    gap: 2rem;
    font-size: 1rem;
    font-weight: 600;
    color: #667eea;
}

.class-meta-info span {
    display: flex;
    align-items: center;
}

.class-meta-info i {
    margin-right: 0.5rem;
    color: #FF0000;
}

.session-actions {
    display: flex;
    gap: 1rem;
}

.athletes-tracking-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.tracking-header h2 {
    color: #2D3748;
    font-size: 1.75rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
}

.tracking-header h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
    animation: pulse 2s infinite;
}

.session-timers {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.session-timer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 1.2rem;
    font-weight: 900;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    color: #FFFFFF;
    padding: 0.75rem 1rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
    min-width: 80px;
}

.session-timer i {
    color: #FFD700;
    animation: tick 1s infinite;
    font-size: 1rem;
}

.timer-label {
    font-size: 0.7rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Class Timer Styles */
.class-timer {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
    border: 3px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    min-width: 300px;
}

.class-timer-display {
    text-align: center;
    margin-bottom: 1rem;
}

.class-timer .timer-time {
    font-size: 3rem;
    font-weight: 900;
    color: #FFFFFF;
    font-family: 'Courier New', monospace;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
}

.class-timer .timer-progress-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.class-timer .timer-progress-bar {
    flex: 1;
    height: 12px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.5);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.class-timer .timer-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10B981, #059669);
    transition: width 0.3s ease, background 0.3s ease;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
}

.class-timer .timer-duration {
    font-size: 1rem;
    font-weight: 700;
    color: #FFFFFF;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.class-timer-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    justify-content: center;
}

.timer-select {
    padding: 0.5rem 0.75rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.9);
    color: #2D3748;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timer-select:focus {
    outline: none;
    border-color: #FFFFFF;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.timer-select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.class-timer-btn {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
}

.class-timer-btn i {
    margin-right: 0.5rem;
}

.class-timer-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.3);
    border-color: #FFFFFF;
}

.class-timer-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.class-timer-btn.start-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #10B981, #059669);
    border-color: #10B981;
}

.class-timer-btn.stop-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #EF4444, #DC2626);
    border-color: #EF4444;
}

.class-timer-btn.reset-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    border-color: #8B5CF6;
}

/* Timer pulse animation */
@keyframes timer-pulse {
    0%, 100% {
        transform: scale(1);
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.05);
        text-shadow: 3px 3px 12px rgba(255, 215, 0, 0.6);
    }
}

.athletes-tracking-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.athlete-tracking-card {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
    position: relative;
}

.athlete-tracking-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.athlete-tracking-card.completed {
    border-left-color: #10B981;
    background: linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 100%);
    opacity: 0.8;
}

.athlete-tracking-card.completed .athlete-tracking-avatar {
    border-color: #10B981;
}

.athlete-card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.athlete-current-score {
    margin-left: auto;
}

.score-display {
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #FFFFFF;
    padding: 0.5rem;
    border-radius: 10px;
    min-width: 60px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.score-display .score-value {
    font-size: 1.5rem;
    font-weight: 900;
    line-height: 1;
    display: block;
}

.score-display .score-label {
    font-size: 0.6rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.athlete-tracking-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 1.4rem;
    font-weight: 900;
    text-transform: uppercase;
    flex-shrink: 0;
}

.athlete-tracking-info h4 {
    font-size: 1.2rem;
    font-weight: 800;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.athlete-tracking-info .nickname {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
    font-style: italic;
    margin-bottom: 0.5rem;
}

.athlete-tracking-info .level {
    font-size: 0.75rem;
    color: #FFFFFF;
    font-weight: 700;
    text-transform: uppercase;
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    display: inline-block;
}



/* Scoring Section */
.scoring-section {
    background: linear-gradient(135deg, #FFF8E1, #FFF3C4);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #FFD700;
}

.scoring-label {
    font-size: 0.9rem;
    font-weight: 700;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
    text-align: center;
}

.scoring-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.score-btn {
    padding: 0.75rem 0.5rem;
    border: 2px solid;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.score-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.score-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.plus-btn {
    background: linear-gradient(135deg, #10B981, #059669);
    color: #FFFFFF;
    border-color: #10B981;
}

.mystery-btn {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: #FFFFFF;
    border-color: #8B5CF6;
    position: relative;
}

.mystery-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: mystery-shine 2s infinite;
}

.miss-btn {
    background: linear-gradient(135deg, #FF0000, #DC2626);
    color: #FFFFFF;
    border-color: #FF0000;
}

/* Score Animation */
.score-animation {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    font-size: 1.2rem;
    font-weight: 900;
    color: #10B981;
    pointer-events: none;
    animation: score-popup 1.5s ease-out forwards;
    z-index: 10;
}

@keyframes score-popup {
    0% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-70%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-90%) scale(0.8);
    }
}

@keyframes mystery-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.notes-section {
    margin-bottom: 1.5rem;
}

.notes-section label {
    display: block;
    font-size: 0.9rem;
    font-weight: 700;
    color: #2D3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.notes-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #E2E8F0;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    resize: vertical;
    min-height: 80px;
    transition: all 0.3s ease;
    background: #FFFFFF;
    font-family: 'Inter', sans-serif;
}

.notes-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-athlete-btn {
    width: 100%;
    padding: 0.75rem;
    background: linear-gradient(135deg, #10B981, #059669);
    color: #FFFFFF;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.submit-athlete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.submit-athlete-btn:disabled {
    background: #9CA3AF;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.submit-athlete-btn i {
    margin-right: 0.5rem;
}

.session-summary {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 2px solid rgba(102, 126, 234, 0.2);
}

/* Scrollbar Styling */
.leaderboard-list::-webkit-scrollbar,
.athletes-tracking-grid::-webkit-scrollbar {
    width: 8px;
}

.leaderboard-list::-webkit-scrollbar-track,
.athletes-tracking-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.leaderboard-list::-webkit-scrollbar-thumb,
.athletes-tracking-grid::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
}

.leaderboard-list::-webkit-scrollbar-thumb:hover,
.athletes-tracking-grid::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #FF0000, #FF6B6B);
}

/* Run Page Responsive Styles */
@media (max-width: 768px) {
    .run-class-selector {
        padding: 1.5rem;
    }

    .class-selector-container {
        flex-direction: column;
        gap: 1rem;
    }

    .class-info-card {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .class-meta-info {
        flex-direction: column;
        gap: 0.75rem;
    }

    .session-actions {
        flex-direction: column;
        width: 100%;
    }

    .athletes-tracking-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .athlete-tracking-card {
        padding: 1.25rem;
    }

    .tracking-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .session-timers {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .class-timer {
        min-width: auto;
        width: 100%;
    }

    .class-timer .timer-time {
        font-size: 2.5rem;
    }

    .class-timer-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .class-timer-btn {
        flex: 1;
        min-width: 80px;
        justify-content: center;
    }

    .timer-select {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .session-summary {
        flex-direction: column;
        gap: 1rem;
    }

    .scoring-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .score-btn {
        padding: 1rem;
        font-size: 1rem;
    }
}

/* Run Page Tablet Styles */
@media (min-width: 481px) and (max-width: 767px) {
    .athletes-tracking-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .class-info-card {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
}

/* Additional animations for Run page */
@keyframes tick {
    0%, 50% { transform: scale(1); }
    25% { transform: scale(1.1); }
}

/* Settings Page Styles */
.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.settings-section {
    background: #FFFFFF;
    border: 3px solid #FF0000;
    margin-bottom: 2rem;
    padding: 2rem;
    box-shadow: 0 8px 24px rgba(255, 0, 0, 0.2);
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #000000;
}

.section-header h2 {
    color: #000000;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.section-header h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.section-header p {
    color: #666666;
    font-size: 1rem;
    font-weight: 500;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.setting-card {
    background: #F8F9FA;
    border: 2px solid #0033FF;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.setting-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 51, 255, 0.15);
}

.setting-card h3 {
    color: #000000;
    font-size: 1.25rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.setting-card h3 i {
    margin-right: 0.5rem;
    color: #0033FF;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    color: #000000;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.setting-group input[type="number"],
.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #000000;
    background: #FFFFFF;
    color: #000000;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.setting-group input[type="number"]:focus,
.setting-group select:focus {
    outline: none;
    border-color: #FF0000;
    box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
}

.setting-group input[type="number"]:disabled,
.setting-group select:disabled {
    background: #E9ECEF;
    color: #6C757D;
    cursor: not-allowed;
}

.setting-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 0.75rem;
    accent-color: #FF0000;
}

.setting-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.setting-description {
    display: block;
    color: #666666;
    font-size: 0.8rem;
    font-weight: 500;
    font-style: italic;
    margin-top: 0.25rem;
}

.settings-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #E9ECEF;
}

.settings-actions .btn {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 150px;
}

/* Settings Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        padding: 1rem;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .settings-actions {
        flex-direction: column;
        align-items: center;
    }

    .settings-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
