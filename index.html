<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ninja Score Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-brand">
            <i class="fas fa-user-ninja"></i>
            <span>Ninja Score Tracker</span>
        </div>
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#" class="nav-link active" data-page="home">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="leaderboard">
                    <i class="fas fa-trophy"></i>
                    <span>Leaderboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="stats">
                    <i class="fas fa-chart-bar"></i>
                    <span>Stats</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="run">
                    <i class="fas fa-play"></i>
                    <span>Run</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Home Page -->
        <div id="home-page" class="page active">
            <div class="page-header">
                <h1><i class="fas fa-home"></i> Home</h1>
                <p>Manage your Ninja Classes</p>
            </div>

            <!-- Add New Athlete Form -->
            <div class="form-container athlete-form-container">
                <h2><i class="fas fa-user-plus"></i> Add New Athlete</h2>
                <form id="add-athlete-form-home">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="athleteNameHome">
                                <i class="fas fa-user"></i> Full Name
                            </label>
                            <input type="text" id="athleteNameHome" name="athleteName" required
                                   placeholder="Enter athlete's full name...">
                        </div>
                        <div class="form-group">
                            <label for="athleteAgeHome">
                                <i class="fas fa-birthday-cake"></i> Age
                            </label>
                            <input type="number" id="athleteAgeHome" name="athleteAge" min="5" max="99" required
                                   placeholder="Age">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="athleteNicknameHome">
                                <i class="fas fa-tag"></i> Nickname (Optional)
                            </label>
                            <input type="text" id="athleteNicknameHome" name="athleteNickname"
                                   placeholder="e.g., Flash, Shadow, Lightning...">
                        </div>
                        <div class="form-group">
                            <label for="athleteLevelHome">
                                <i class="fas fa-star"></i> Level
                            </label>
                            <select id="athleteLevelHome" name="athleteLevel" required>
                                <option value="">Select Level</option>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="athleteAvatarHome">
                                <i class="fas fa-image"></i> Avatar (Optional)
                            </label>
                            <input type="file" id="athleteAvatarHome" name="athleteAvatar"
                                   accept="image/*" class="file-input">
                        </div>
                        <div class="form-group">
                            <!-- Empty space for layout balance -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="athleteDescriptionHome">
                            <i class="fas fa-align-left"></i> Description (Optional)
                        </label>
                        <textarea id="athleteDescriptionHome" name="athleteDescription"
                                  placeholder="Brief description about the athlete..." rows="3"></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Add Athlete
                    </button>
                </form>
            </div>

            <!-- Add New Class Form -->
            <div class="form-container">
                <h2><i class="fas fa-plus-circle"></i> Add New Ninja Class</h2>
                <form id="add-class-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="className">
                                <i class="fas fa-tag"></i> Class Name
                            </label>
                            <input type="text" id="className" name="className" required 
                                   placeholder="Enter class name...">
                        </div>
                        <div class="form-group">
                            <label for="classDate">
                                <i class="fas fa-calendar"></i> Date
                            </label>
                            <input type="date" id="classDate" name="classDate" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="classTime">
                                <i class="fas fa-clock"></i> Time
                            </label>
                            <input type="time" id="classTime" name="classTime" required>
                        </div>
                        <div class="form-group">
                            <label for="classDescription">
                                <i class="fas fa-align-left"></i> Description
                            </label>
                            <textarea id="classDescription" name="classDescription" 
                                      placeholder="Enter class description..." rows="3"></textarea>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Class
                    </button>
                </form>
            </div>

            <!-- Classes List -->
            <div class="classes-container">
                <h2><i class="fas fa-list"></i> Your Classes</h2>
                <div id="classes-list" class="classes-list">
                    <!-- Classes will be dynamically loaded here -->
                </div>
            </div>
        </div>

        <!-- Leaderboard Page -->
        <div id="leaderboard-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-trophy"></i> Leaderboard</h1>
                <p>Top Performing Ninja Athletes</p>
            </div>

            <!-- Leaderboard Container -->
            <div class="leaderboard-container">
                <div class="leaderboard-header">
                    <h2><i class="fas fa-medal"></i> Rankings</h2>
                    <div class="leaderboard-stats">
                        <span id="total-athletes-lb">0 Athletes</span>
                        <span class="separator">•</span>
                        <span id="last-updated-lb">Last Updated: Now</span>
                    </div>
                </div>

                <!-- Leaderboard List -->
                <div id="leaderboard-list" class="leaderboard-list">
                    <!-- Athletes will be dynamically loaded here -->
                </div>

                <!-- Empty State -->
                <div id="empty-leaderboard" class="empty-state hidden">
                    <i class="fas fa-trophy"></i>
                    <h3>No Athletes Yet</h3>
                    <p>Add athletes from the Home page to see them on the leaderboard!</p>
                </div>
            </div>
        </div>

        <!-- Stats Page -->
        <div id="stats-page" class="page">
            <div class="coming-soon">
                <i class="fas fa-chart-bar"></i>
                <h2>🚧 Coming Soon</h2>
                <p>Statistics and analytics will be available in a future update.</p>
            </div>
        </div>

        <!-- Run Page -->
        <div id="run-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-play"></i> Live Run Session</h1>
                <p>Track Scores & Performance During Class</p>
            </div>

            <!-- Class Selector Section -->
            <div class="run-class-selector">
                <h2><i class="fas fa-graduation-cap"></i> Select Class</h2>
                <div class="class-selector-container">
                    <select id="run-class-select" class="class-select">
                        <option value="">Choose a class to start tracking...</option>
                    </select>
                    <button id="load-class-btn" class="btn btn-primary" disabled>
                        <i class="fas fa-users"></i>
                        Load Athletes
                    </button>
                </div>
            </div>

            <!-- Selected Class Info -->
            <div id="selected-class-info" class="selected-class-info hidden">
                <div class="class-info-card">
                    <div class="class-details">
                        <h3 id="selected-class-name">Class Name</h3>
                        <div class="class-meta-info">
                            <span id="selected-class-date">
                                <i class="fas fa-calendar"></i>
                                Date
                            </span>
                            <span id="selected-class-time">
                                <i class="fas fa-clock"></i>
                                Time
                            </span>
                            <span id="selected-athlete-count">
                                <i class="fas fa-users"></i>
                                0 Athletes
                            </span>
                        </div>
                    </div>
                    <div class="session-actions">
                        <button id="start-session-btn" class="btn btn-primary">
                            <i class="fas fa-play"></i>
                            Start Session
                        </button>
                        <button id="end-session-btn" class="btn btn-secondary hidden">
                            <i class="fas fa-stop"></i>
                            End Session
                        </button>
                    </div>
                </div>
            </div>

            <!-- Athletes Tracking Grid -->
            <div id="athletes-tracking-section" class="athletes-tracking-section hidden">
                <div class="tracking-header">
                    <h2><i class="fas fa-stopwatch"></i> Live Tracking</h2>
                    <div class="session-timers">
                        <div class="session-timer">
                            <i class="fas fa-clock"></i>
                            <span id="session-timer">00:00</span>
                            <span class="timer-label">Session</span>
                        </div>
                        <div class="class-timer">
                            <div class="class-timer-display">
                                <div class="timer-time" id="class-timer-display">00:00</div>
                                <div class="timer-progress-container">
                                    <div class="timer-progress-bar">
                                        <div class="timer-progress-fill" id="class-timer-progress" style="width: 0%"></div>
                                    </div>
                                    <div class="timer-duration" id="class-timer-duration">/ 5:00</div>
                                </div>
                            </div>
                            <div class="class-timer-controls">
                                <select id="timer-minutes-select" class="timer-select">
                                    <option value="1">1 min</option>
                                    <option value="2">2 min</option>
                                    <option value="3">3 min</option>
                                    <option value="5" selected>5 min</option>
                                    <option value="10">10 min</option>
                                    <option value="15">15 min</option>
                                    <option value="20">20 min</option>
                                    <option value="30">30 min</option>
                                    <option value="45">45 min</option>
                                    <option value="60">60 min</option>
                                </select>
                                <button id="class-timer-start" class="class-timer-btn start-btn">
                                    <i class="fas fa-play"></i>
                                    Start
                                </button>
                                <button id="class-timer-stop" class="class-timer-btn stop-btn" disabled>
                                    <i class="fas fa-stop"></i>
                                    Stop
                                </button>
                                <button id="class-timer-reset" class="class-timer-btn reset-btn">
                                    <i class="fas fa-undo"></i>
                                    Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="athletes-tracking-grid" class="athletes-tracking-grid">
                    <!-- Athlete tracking cards will be dynamically loaded here -->
                </div>

                <!-- Session Summary -->
                <div class="session-summary">
                    <button id="save-all-scores-btn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save All Scores
                    </button>
                    <button id="clear-all-scores-btn" class="btn btn-secondary">
                        <i class="fas fa-eraser"></i>
                        Clear All
                    </button>
                </div>
            </div>

            <!-- Empty State -->
            <div id="run-empty-state" class="empty-state">
                <i class="fas fa-play-circle"></i>
                <h3>Ready to Start Tracking</h3>
                <p>Select a class above to begin tracking athlete performance during your session.</p>
            </div>
        </div>

        <!-- Settings Page -->
        <div id="settings-page" class="page">
            <div class="page-header">
                <h1><i class="fas fa-cog"></i> Game Settings</h1>
                <p>Configure your ninja warrior scoring system and game mechanics</p>
            </div>

            <!-- Settings Container -->
            <div class="settings-container">

                <!-- Scoring System Section -->
                <div class="settings-section">
                    <div class="section-header">
                        <h2><i class="fas fa-trophy"></i> Scoring System</h2>
                        <p>Configure point values and scoring mechanics</p>
                    </div>

                    <div class="settings-grid">
                        <!-- Basic Point Values -->
                        <div class="setting-card">
                            <h3><i class="fas fa-plus-circle"></i> Basic Point Values</h3>
                            <div class="setting-group">
                                <label for="small-points">Small Achievement (+1 Button)</label>
                                <input type="number" id="small-points" min="1" max="10" value="1">
                                <span class="setting-description">Points for basic moves, attempts, effort</span>
                            </div>
                            <div class="setting-group">
                                <label for="medium-points">Medium Achievement (+2 Button)</label>
                                <input type="number" id="medium-points" min="1" max="20" value="2">
                                <span class="setting-description">Points for good technique, partial completion</span>
                            </div>
                            <div class="setting-group">
                                <label for="large-points">Large Achievement (+5 Button)</label>
                                <input type="number" id="large-points" min="1" max="50" value="5">
                                <span class="setting-description">Points for obstacle completion, great form</span>
                            </div>
                            <div class="setting-group">
                                <label for="mega-points">Mega Achievement (+10 Button)</label>
                                <input type="number" id="mega-points" min="1" max="100" value="10">
                                <span class="setting-description">Points for perfect runs, exceptional performance</span>
                            </div>
                        </div>

                        <!-- Special Scoring -->
                        <div class="setting-card">
                            <h3><i class="fas fa-magic"></i> Special Scoring</h3>
                            <div class="setting-group">
                                <label for="mystery-min">Mystery Box Minimum</label>
                                <input type="number" id="mystery-min" min="1" max="50" value="1">
                                <span class="setting-description">Minimum points from mystery bonus</span>
                            </div>
                            <div class="setting-group">
                                <label for="mystery-max">Mystery Box Maximum</label>
                                <input type="number" id="mystery-max" min="1" max="100" value="10">
                                <span class="setting-description">Maximum points from mystery bonus</span>
                            </div>
                            <div class="setting-group">
                                <label for="miss-penalty">Miss Penalty</label>
                                <input type="number" id="miss-penalty" min="0" max="10" value="1">
                                <span class="setting-description">Points deducted for misses (0 = no penalty)</span>
                            </div>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="allow-negative" checked>
                                    Allow Negative Scores
                                </label>
                                <span class="setting-description">Allow athlete scores to go below zero</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bonus Multipliers Section -->
                <div class="settings-section">
                    <div class="section-header">
                        <h2><i class="fas fa-rocket"></i> Bonus Multipliers</h2>
                        <p>Reward exceptional performance with multipliers</p>
                    </div>

                    <div class="settings-grid">
                        <div class="setting-card">
                            <h3><i class="fas fa-stopwatch"></i> Speed Bonuses</h3>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="speed-bonus-enabled">
                                    Enable Speed Bonuses
                                </label>
                                <span class="setting-description">Award extra points for fast completion</span>
                            </div>
                            <div class="setting-group">
                                <label for="speed-threshold">Speed Threshold (seconds)</label>
                                <input type="number" id="speed-threshold" min="5" max="300" value="30" disabled>
                                <span class="setting-description">Complete under this time for bonus</span>
                            </div>
                            <div class="setting-group">
                                <label for="speed-multiplier">Speed Multiplier</label>
                                <select id="speed-multiplier" disabled>
                                    <option value="1.2">1.2x (20% bonus)</option>
                                    <option value="1.5" selected>1.5x (50% bonus)</option>
                                    <option value="2.0">2.0x (Double points)</option>
                                </select>
                            </div>
                        </div>

                        <div class="setting-card">
                            <h3><i class="fas fa-fire"></i> Streak Bonuses</h3>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="streak-bonus-enabled">
                                    Enable Streak Bonuses
                                </label>
                                <span class="setting-description">Bonus for consecutive successes</span>
                            </div>
                            <div class="setting-group">
                                <label for="streak-threshold">Streak Threshold</label>
                                <input type="number" id="streak-threshold" min="2" max="10" value="3" disabled>
                                <span class="setting-description">Consecutive successes needed</span>
                            </div>
                            <div class="setting-group">
                                <label for="streak-bonus">Streak Bonus Points</label>
                                <input type="number" id="streak-bonus" min="1" max="50" value="5" disabled>
                                <span class="setting-description">Extra points per streak achievement</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Actions -->
                <div class="settings-actions">
                    <button id="save-settings-btn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Settings
                    </button>
                    <button id="reset-settings-btn" class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        Reset to Defaults
                    </button>
                    <button id="export-settings-btn" class="btn btn-outline">
                        <i class="fas fa-download"></i>
                        Export Settings
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Indicator -->
    <div id="loading" class="loading hidden">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- JavaScript -->
    <script src="renderer.js"></script>
</body>
</html>
