/* Leaderboard Page Specific Styles */

/* Leaderboard Container */
.leaderboard-container {
    background: #FFFFFF;
    border: 3px solid #FF0000;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(255, 0, 0, 0.2);
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #000000;
}

.leaderboard-header h2 {
    color: #000000;
    font-size: 1.75rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
}

.leaderboard-header h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
    font-size: 1.5rem;
}

.leaderboard-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
}

.leaderboard-stats .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border: 2px solid #0033FF;
    background: #FFFFFF;
    color: #0033FF;
    transition: all 0.2s ease;
}

.leaderboard-stats .btn:hover {
    background: #0033FF;
    color: #FFFFFF;
    transform: translateY(-1px);
}

.leaderboard-stats .btn.auto-refreshed {
    background: #00FF00;
    color: #000000;
    border-color: #00FF00;
    animation: pulse-green 0.5s ease-in-out;
}

@keyframes pulse-green {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.separator {
    color: #FF0000;
    font-weight: 800;
}

/* Leaderboard List */
.leaderboard-list {
    max-height: 600px;
    overflow-y: auto;
}

/* Athlete Card in Leaderboard */
.leaderboard-athlete {
    background: #FFFFFF;
    border: 2px solid #000000;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    border-left: 6px solid #0033FF;
}

.leaderboard-athlete:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-left-color: #FF0000;
}

/* Ranking Number */
.athlete-rank {
    font-size: 2rem;
    font-weight: 800;
    color: #000000;
    min-width: 3rem;
    text-align: center;
    text-transform: uppercase;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.athlete-rank.top-3 {
    color: #FF0000;
}

/* Medal Icons for Top 3 */
.medal-icon {
    font-size: 1.5rem;
    z-index: 10;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.medal-gold {
    color: #FFD700;
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
}

.medal-silver {
    color: #C0C0C0;
    filter: drop-shadow(0 0 8px rgba(192, 192, 192, 0.6));
}

.medal-bronze {
    color: #CD7F32;
    filter: drop-shadow(0 0 8px rgba(205, 127, 50, 0.6));
}

/* Athlete Avatar */
.leaderboard-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #FF0000;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 1.4rem;
    font-weight: 800;
    text-transform: uppercase;
    flex-shrink: 0;
}

/* Athlete Info */
.athlete-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.athlete-name {
    font-size: 1.2rem;
    font-weight: 800;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.athlete-nickname {
    font-size: 0.9rem;
    color: #FF0000;
    font-weight: 600;
    font-style: italic;
}

.athlete-level {
    font-size: 0.8rem;
    color: #0033FF;
    font-weight: 600;
    text-transform: uppercase;
}

/* XP Progress Bar */
.xp-container {
    margin-top: 0.5rem;
}

.xp-label {
    font-size: 0.8rem;
    color: #000000;
    font-weight: 600;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.xp-bar {
    width: 100%;
    height: 8px;
    background: #E0E0E0;
    border: 1px solid #000000;
    position: relative;
    overflow: hidden;
}

.xp-progress {
    height: 100%;
    background: linear-gradient(90deg, #0033FF, #FF0000);
    transition: width 0.3s ease;
}

.xp-text {
    font-size: 0.8rem;
    color: #000000;
    font-weight: 600;
    margin-top: 0.25rem;
}

/* Score Display */
.athlete-score {
    text-align: center;
    min-width: 100px;
}

.score-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: #FF0000;
    line-height: 1;
}

.score-label {
    font-size: 0.8rem;
    color: #000000;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Podium Section */
.podium-section {
    background: #000000;
    color: #FFFFFF;
    padding: 3rem 2rem;
    margin-bottom: 2rem;
    border: 3px solid #FF0000;
    text-align: center;
}

.podium-section h2 {
    color: #FFFFFF;
    font-size: 2rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.podium-section h2 i {
    margin-right: 0.75rem;
    color: #FFD700;
    font-size: 1.8rem;
}

.podium {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.podium-place {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.podium-athlete {
    margin-bottom: 1rem;
    text-align: center;
}

.podium-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 2rem;
    font-weight: 800;
    text-transform: uppercase;
    margin: 0 auto 0.5rem;
}

.podium-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: #FFFFFF;
    margin-bottom: 0.25rem;
}

.podium-score {
    font-size: 1.5rem;
    font-weight: 800;
    color: #FF0000;
}

.podium-base {
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    text-transform: uppercase;
    border: 2px solid;
}

.podium-base i {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.gold {
    background: #FFD700;
    color: #000000;
    border-color: #FFD700;
}

.silver {
    background: #C0C0C0;
    color: #000000;
    border-color: #C0C0C0;
}

.bronze {
    background: #CD7F32;
    color: #FFFFFF;
    border-color: #CD7F32;
}

.first-place .podium-base {
    height: 100px;
}

.second-place .podium-base {
    height: 80px;
}

.third-place .podium-base {
    height: 60px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #000000;
}

.empty-state i {
    font-size: 4rem;
    color: #FF0000;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
}

.empty-state p {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 2rem;
}

/* Auto-refresh notification */
.auto-refresh-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #0033FF;
    color: #FFFFFF;
    padding: 1rem 1.5rem;
    border: 2px solid #FF0000;
    font-weight: 600;
    font-size: 0.9rem;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.auto-refresh-notification.show {
    transform: translateX(0);
}

.auto-refresh-notification i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .leaderboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .leaderboard-athlete {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .athlete-rank {
        font-size: 1.5rem;
    }

    .leaderboard-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.4rem;
    }

    .podium {
        flex-direction: column;
        gap: 1rem;
    }

    .podium-place {
        width: 100%;
    }

    .podium-base {
        width: 100px;
        height: 60px;
    }

    .first-place .podium-base,
    .second-place .podium-base,
    .third-place .podium-base {
        height: 60px;
    }

    .auto-refresh-notification {
        top: 10px;
        right: 10px;
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }
}
