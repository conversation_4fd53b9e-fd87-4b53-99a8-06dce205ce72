{"name": "ninja-score-tracker", "version": "1.0.0", "description": "An offline desktop app for tracking ninja class scores and managing athletes", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "ninja", "score", "tracker", "offline", "desktop"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^36.4.0"}}