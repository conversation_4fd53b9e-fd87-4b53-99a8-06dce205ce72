/* Roster Page Specific Styles */

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
}

/* Class Header */
.class-header {
    background: #000000;
    color: #FFFFFF;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 3px solid #FF0000;
    border-radius: 0;
}

.class-info h1 {
    font-size: 2.5rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    color: #FFFFFF;
}

.class-meta {
    display: flex;
    gap: 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.class-meta span {
    display: flex;
    align-items: center;
    color: #FFFFFF;
}

.class-meta i {
    margin-right: 0.5rem;
    color: #FF0000;
}

/* Manage Section */
.manage-section {
    background: #FFFFFF;
    border: 3px solid #0033FF;
    padding: 2rem;
    margin-bottom: 2rem;
}

.manage-section h2 {
    color: #000000;
    font-size: 1.5rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.manage-section h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Roster Section */
.roster-section {
    background: #FFFFFF;
    border: 3px solid #FF0000;
    padding: 2rem;
}

.roster-section h2 {
    color: #000000;
    font-size: 1.5rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
}

.roster-section h2 i {
    margin-right: 0.75rem;
    color: #FF0000;
}

/* Roster Grid */
.roster-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
}

/* Athlete Card */
.athlete-card {
    background: #FFFFFF;
    border: 2px solid #000000;
    padding: 1.5rem;
    transition: all 0.2s ease;
    position: relative;
    border-left: 6px solid #0033FF;
}

.athlete-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border-left-color: #FF0000;
}

.athlete-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.athlete-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #FF0000;
    background: linear-gradient(135deg, #FF0000, #0033FF);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-size: 1.5rem;
    font-weight: 800;
    margin-right: 1rem;
    text-transform: uppercase;
}

.athlete-info h3 {
    font-size: 1.2rem;
    font-weight: 800;
    color: #000000;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.athlete-nickname {
    font-size: 0.9rem;
    color: #FF0000;
    font-weight: 600;
    font-style: italic;
}

.athlete-details {
    margin-bottom: 1rem;
}

.athlete-description {
    color: #000000;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.athlete-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.level-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 0;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid;
}

.level-beginner {
    background: #FFFFFF;
    color: #0033FF;
    border-color: #0033FF;
}

.level-intermediate {
    background: #FFFFFF;
    color: #FF0000;
    border-color: #FF0000;
}

.level-advanced {
    background: #000000;
    color: #FFFFFF;
    border-color: #000000;
}

.sessions-count {
    font-size: 0.9rem;
    font-weight: 600;
    color: #000000;
}

.athlete-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-remove {
    background: #FF0000;
    color: #FFFFFF;
    border: 2px solid #FF0000;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-remove:hover {
    background: #000000;
    border-color: #000000;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: #FFFFFF;
    border: 3px solid #000000;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    padding: 0;
}

.modal-header {
    background: #000000;
    color: #FFFFFF;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid #FF0000;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
}

.modal-header h3 i {
    margin-right: 0.75rem;
    color: #FF0000;
}

.modal-close {
    background: none;
    border: none;
    color: #FFFFFF;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #FF0000;
}

.modal form {
    padding: 2rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #000000;
}

/* Search and Available Athletes */
.search-box {
    position: relative;
    margin: 2rem;
    margin-bottom: 1rem;
}

.search-box input {
    width: 100%;
    padding: 1rem;
    padding-right: 3rem;
    border: 2px solid #000000;
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #FF0000;
    font-size: 1.2rem;
}

.available-athletes {
    max-height: 400px;
    overflow-y: auto;
    padding: 0 2rem 2rem;
}

.available-athlete {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 2px solid #000000;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.available-athlete:hover {
    background: #F5F5F5;
    border-color: #0033FF;
}

.available-athlete-info {
    display: flex;
    align-items: center;
}

.available-athlete-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #0033FF;
    background: linear-gradient(135deg, #0033FF, #FF0000);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFFFFF;
    font-weight: 800;
    margin-right: 1rem;
    text-transform: uppercase;
}

.btn-add-existing {
    background: #0033FF;
    color: #FFFFFF;
    border: 2px solid #0033FF;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
}

.btn-add-existing:hover {
    background: #FFFFFF;
    color: #0033FF;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #000000;
}

.empty-state i {
    font-size: 4rem;
    color: #FF0000;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
}

.empty-state p {
    font-size: 1rem;
    font-weight: 500;
}

/* Placeholder Athlete Card Styles */
.placeholder-card {
    opacity: 0.8;
    border-style: dashed !important;
    border-color: #0033FF !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.95)) !important;
    position: relative;
    overflow: hidden;
}

.placeholder-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 51, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.placeholder-card:hover {
    transform: translateY(-2px);
    opacity: 1;
    border-color: #FF0000 !important;
}

.placeholder-avatar {
    background: linear-gradient(135deg, #0033FF, #FF0000) !important;
    border-color: #0033FF !important;
    animation: pulse 2s infinite;
}

.placeholder-badge {
    background: #0033FF;
    color: #FFFFFF;
    padding: 0.5rem 1rem;
    border-radius: 0;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid #0033FF;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.placeholder-card .athlete-nickname {
    color: #0033FF !important;
}

.placeholder-card .athlete-description {
    font-style: italic;
    color: #555 !important;
}

.placeholder-card .sessions-count {
    color: #0033FF !important;
    font-weight: 600;
}

/* Shimmer animation for placeholder cards */
@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Pulse animation for placeholder avatars */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .class-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .roster-grid {
        grid-template-columns: 1fr;
    }
    
    .athlete-stats {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .available-athlete {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
