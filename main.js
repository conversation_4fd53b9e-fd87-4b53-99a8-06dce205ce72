const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;

// Keep a global reference of the window object
let mainWindow;

// Path to the data files
const CLASSES_FILE_PATH = path.join(__dirname, 'classes.json');
const ATHLETES_FILE_PATH = path.join(__dirname, 'athletes.json');
const RUN_SESSIONS_FILE_PATH = path.join(__dirname, 'runSessions.json');
const SETTINGS_FILE_PATH = path.join(__dirname, 'settings.json');

/**
 * Create the main application window
 */
function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets', 'icon.png'), // Optional: Add app icon
        title: 'Ninja Score Tracker'
    });

    // Load the index.html file
    mainWindow.loadFile('index.html');

    // Open DevTools in development (comment out for production)
    // mainWindow.webContents.openDevTools();

    // Handle window closed
    mainWindow.on('closed', function () {
        mainWindow = null;
    });
}

/**
 * Initialize the data files if they don't exist
 */
async function initializeDataFiles() {
    // Initialize classes file
    try {
        await fs.access(CLASSES_FILE_PATH);
        console.log('Classes file exists');
    } catch (error) {
        console.log('Creating classes file...');
        await fs.writeFile(CLASSES_FILE_PATH, JSON.stringify([], null, 2));
    }

    // Initialize athletes file
    try {
        await fs.access(ATHLETES_FILE_PATH);
        console.log('Athletes file exists');
    } catch (error) {
        console.log('Creating athletes file...');
        await fs.writeFile(ATHLETES_FILE_PATH, JSON.stringify([], null, 2));
    }

    // Initialize run sessions file
    try {
        await fs.access(RUN_SESSIONS_FILE_PATH);
        console.log('Run sessions file exists');
    } catch (error) {
        console.log('Creating run sessions file...');
        await fs.writeFile(RUN_SESSIONS_FILE_PATH, JSON.stringify([], null, 2));
    }

    // Initialize settings file
    try {
        await fs.access(SETTINGS_FILE_PATH);
        console.log('Settings file exists');
    } catch (error) {
        console.log('Creating settings file...');
        const defaultSettings = {
            smallPoints: 1,
            mediumPoints: 2,
            largePoints: 5,
            megaPoints: 10,
            mysteryMin: 1,
            mysteryMax: 10,
            missPenalty: 1,
            allowNegative: true,
            speedBonusEnabled: false,
            speedThreshold: 30,
            speedMultiplier: 1.5,
            streakBonusEnabled: false,
            streakThreshold: 3,
            streakBonus: 5
        };
        await fs.writeFile(SETTINGS_FILE_PATH, JSON.stringify(defaultSettings, null, 2));
    }
}

/**
 * Load classes from the JSON file
 */
async function loadClasses() {
    try {
        const data = await fs.readFile(CLASSES_FILE_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading classes:', error);
        return [];
    }
}

/**
 * Save classes to the JSON file
 */
async function saveClasses(classes) {
    try {
        await fs.writeFile(CLASSES_FILE_PATH, JSON.stringify(classes, null, 2));
        console.log('Classes saved successfully');
        return true;
    } catch (error) {
        console.error('Error saving classes:', error);
        return false;
    }
}

/**
 * Load athletes from the JSON file
 */
async function loadAthletes() {
    try {
        const data = await fs.readFile(ATHLETES_FILE_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading athletes:', error);
        return [];
    }
}

/**
 * Save athletes to the JSON file
 */
async function saveAthletes(athletes) {
    try {
        await fs.writeFile(ATHLETES_FILE_PATH, JSON.stringify(athletes, null, 2));
        console.log('Athletes saved successfully');
        return true;
    } catch (error) {
        console.error('Error saving athletes:', error);
        return false;
    }
}

/**
 * Load run sessions from the JSON file
 */
async function loadRunSessions() {
    try {
        const data = await fs.readFile(RUN_SESSIONS_FILE_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading run sessions:', error);
        return [];
    }
}

/**
 * Save run sessions to the JSON file
 */
async function saveRunSessions(runSessions) {
    try {
        await fs.writeFile(RUN_SESSIONS_FILE_PATH, JSON.stringify(runSessions, null, 2));
        console.log('Run sessions saved successfully');

        // Broadcast to all renderer processes that run sessions have been updated
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('run-sessions-updated', runSessions);
        }

        return true;
    } catch (error) {
        console.error('Error saving run sessions:', error);
        return false;
    }
}

/**
 * Load settings from the JSON file
 */
async function loadSettings() {
    try {
        const data = await fs.readFile(SETTINGS_FILE_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading settings:', error);
        return null;
    }
}

/**
 * Save settings to the JSON file
 */
async function saveSettings(settings) {
    try {
        await fs.writeFile(SETTINGS_FILE_PATH, JSON.stringify(settings, null, 2));
        console.log('Settings saved successfully');
        return true;
    } catch (error) {
        console.error('Error saving settings:', error);
        return false;
    }
}

// App event handlers
app.whenReady().then(async () => {
    await initializeDataFiles();
    createWindow();

    app.on('activate', function () {
        // On macOS, re-create window when dock icon is clicked
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
});

// Quit when all windows are closed
app.on('window-all-closed', function () {
    // On macOS, keep app running even when all windows are closed
    if (process.platform !== 'darwin') app.quit();
});

// IPC handlers for communication with renderer process
ipcMain.handle('load-classes', async () => {
    return await loadClasses();
});

ipcMain.handle('save-class', async (event, newClass) => {
    const classes = await loadClasses();
    
    // Add unique ID and creation timestamp
    newClass.id = Date.now().toString();
    newClass.createdAt = new Date().toISOString();
    newClass.athleteCount = 0; // Initialize athlete count
    
    classes.push(newClass);
    const success = await saveClasses(classes);
    
    if (success) {
        return { success: true, class: newClass };
    } else {
        return { success: false, error: 'Failed to save class' };
    }
});

ipcMain.handle('get-classes', async () => {
    return await loadClasses();
});

ipcMain.handle('save-classes', async (event, classes) => {
    const success = await saveClasses(classes);
    return { success };
});

// IPC handlers for athlete data
ipcMain.handle('load-athletes', async () => {
    return await loadAthletes();
});

ipcMain.handle('save-athletes', async (event, athletes) => {
    const success = await saveAthletes(athletes);
    return { success };
});

ipcMain.handle('get-athletes', async () => {
    return await loadAthletes();
});

// IPC handlers for run sessions data
ipcMain.handle('load-run-sessions', async () => {
    return await loadRunSessions();
});

ipcMain.handle('save-run-sessions', async (event, runSessions) => {
    const success = await saveRunSessions(runSessions);
    return { success };
});

ipcMain.handle('get-run-sessions', async () => {
    return await loadRunSessions();
});

// IPC handlers for settings data
ipcMain.handle('load-settings', async () => {
    return await loadSettings();
});

ipcMain.handle('save-settings', async (event, settings) => {
    const success = await saveSettings(settings);
    return { success };
});

console.log('Ninja Score Tracker - Main process started');
