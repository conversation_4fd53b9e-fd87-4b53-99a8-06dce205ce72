const { ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * DOM Elements
 */
const leaderboardList = document.getElementById('leaderboard-list');
const emptyLeaderboard = document.getElementById('empty-leaderboard');
const podiumSection = document.getElementById('podium-section');
const totalAthletesEl = document.getElementById('total-athletes');
const lastUpdatedEl = document.getElementById('last-updated');
const loadingIndicator = document.getElementById('loading');

/**
 * Application State
 */
let allAthletes = [];
let runSessions = [];

/**
 * Mock data for when athletes.json is empty
 */
const mockAthletes = [
    {
        id: 'mock-1',
        name: '<PERSON>',
        nickname: '<PERSON>',
        level: 'Level 3',
        score: 0,
        xp: 85,
        maxXp: 100
    },
    {
        id: 'mock-2',
        name: '<PERSON>',
        nickname: '<PERSON>',
        level: 'Level 2',
        score: 0,
        xp: 60,
        maxXp: 100
    },
    {
        id: 'mock-3',
        name: '<PERSON>',
        nickname: '<PERSON>',
        level: 'Level 4',
        score: 0,
        xp: 95,
        maxXp: 100
    },
    {
        id: 'mock-4',
        name: '<PERSON>',
        nickname: 'Ice',
        level: 'Level 1',
        score: 0,
        xp: 30,
        maxXp: 100
    },
    {
        id: 'mock-5',
        name: 'Casey Wind',
        nickname: 'Breeze',
        level: 'Level 2',
        score: 0,
        xp: 70,
        maxXp: 100
    }
];

/**
 * Initialize the leaderboard page
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Leaderboard page - Renderer process started');

    // Load athlete data and run sessions
    await loadAthleteData();
    await loadRunSessions();

    // Render the leaderboard
    renderLeaderboard();

    // Update last updated time
    updateLastUpdated();

    // Add refresh button event listener
    const refreshBtn = document.getElementById('refresh-leaderboard');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshLeaderboard);
    }
});

/**
 * Load athlete data from storage
 */
async function loadAthleteData() {
    showLoading(true);
    
    try {
        allAthletes = await ipcRenderer.invoke('load-athletes');
        console.log(`Loaded ${allAthletes.length} athletes from database`);
        
        // If no athletes exist, use mock data for demonstration
        if (allAthletes.length === 0) {
            console.log('No athletes found, using mock data');
            allAthletes = mockAthletes.map(athlete => ({
                ...athlete,
                score: calculateAthleteScore(athlete.id) // Calculate live score even for mock data
            }));
        } else {
            // Convert real athletes to leaderboard format with live scores
            allAthletes = allAthletes.map(athlete => ({
                ...athlete,
                score: calculateAthleteScore(athlete.id), // Calculate live score from run sessions
                xp: Math.floor(Math.random() * 100) + 1, // Mock XP for now
                maxXp: 100,
                level: athlete.level ? `Level ${getLevelNumber(athlete.level)}` : 'Level 1'
            }));
        }
        
    } catch (error) {
        console.error('Error loading athlete data:', error);
        // Fall back to mock data on error
        allAthletes = mockAthletes;
    } finally {
        showLoading(false);
    }
}

/**
 * Load run sessions data from storage
 */
async function loadRunSessions() {
    try {
        runSessions = await ipcRenderer.invoke('load-run-sessions') || [];
        console.log(`Loaded ${runSessions.length} run sessions for leaderboard`);
    } catch (error) {
        console.error('Error loading run sessions:', error);
        runSessions = [];
    }
}

/**
 * Calculate total score for an athlete from all their run sessions
 */
function calculateAthleteScore(athleteId) {
    if (!runSessions || runSessions.length === 0) {
        console.log(`No run sessions available for athlete ${athleteId}`);
        return 0;
    }

    // Find all run sessions for this athlete and sum their scores
    const athleteRuns = runSessions.filter(run => run.athleteId === athleteId);
    const totalScore = athleteRuns.reduce((sum, run) => sum + (run.score || 0), 0);

    console.log(`Athlete ${athleteId}: ${athleteRuns.length} runs, total score: ${totalScore}`);
    return totalScore;
}

/**
 * Convert level string to number for display
 */
function getLevelNumber(level) {
    switch (level.toLowerCase()) {
        case 'beginner': return 1;
        case 'intermediate': return 2;
        case 'advanced': return 3;
        default: return 1;
    }
}

/**
 * Render the leaderboard
 */
function renderLeaderboard() {
    if (allAthletes.length === 0) {
        showEmptyState();
        return;
    }
    
    // Sort athletes by score (descending), then by XP (descending)
    const sortedAthletes = [...allAthletes].sort((a, b) => {
        if (b.score !== a.score) {
            return b.score - a.score;
        }
        return b.xp - a.xp;
    });
    
    // Update stats
    totalAthletesEl.textContent = `${sortedAthletes.length} Athletes`;
    
    // Show main leaderboard
    leaderboardList.style.display = 'block';
    emptyLeaderboard.classList.add('hidden');
    
    // Render athlete cards
    leaderboardList.innerHTML = sortedAthletes.map((athlete, index) => {
        const rank = index + 1;
        const isTop3 = rank <= 3;
        
        return `
            <div class="leaderboard-athlete" data-athlete-id="${athlete.id}">
                <div class="athlete-rank ${isTop3 ? 'top-3' : ''}">
                    ${rank}
                    ${isTop3 ? getMedalIcon(rank) : ''}
                </div>

                <div class="leaderboard-avatar">
                    ${getAthleteInitials(athlete.name)}
                </div>

                <div class="athlete-info">
                    <div class="athlete-name">${escapeHtml(athlete.name)}</div>
                    ${athlete.nickname ? `<div class="athlete-nickname">"${escapeHtml(athlete.nickname)}"</div>` : ''}
                    <div class="athlete-level">${athlete.level}</div>

                    <div class="xp-container">
                        <div class="xp-label">Experience Points</div>
                        <div class="xp-bar">
                            <div class="xp-progress" style="width: ${(athlete.xp / athlete.maxXp) * 100}%"></div>
                        </div>
                        <div class="xp-text">${athlete.xp}/${athlete.maxXp} XP</div>
                    </div>
                </div>

                <div class="athlete-score">
                    <div class="score-value">${athlete.score}</div>
                    <div class="score-label">Total Score</div>
                </div>
            </div>
        `;
    }).join('');
    
    // Render podium for top 3
    if (sortedAthletes.length >= 3) {
        renderPodium(sortedAthletes.slice(0, 3));
    }
}

/**
 * Get medal icon for top 3 positions
 */
function getMedalIcon(rank) {
    const medals = {
        1: '<i class="fas fa-crown medal-icon medal-gold"></i>',
        2: '<i class="fas fa-medal medal-icon medal-silver"></i>',
        3: '<i class="fas fa-medal medal-icon medal-bronze"></i>'
    };
    return medals[rank] || '';
}

/**
 * Render the podium section
 */
function renderPodium(topThree) {
    podiumSection.classList.remove('hidden');
    
    // Arrange as [2nd, 1st, 3rd] for visual podium effect
    const podiumOrder = [topThree[1], topThree[0], topThree[2]];
    const positions = ['second-place-athlete', 'first-place-athlete', 'third-place-athlete'];
    
    podiumOrder.forEach((athlete, index) => {
        if (athlete) {
            const element = document.getElementById(positions[index]);
            element.innerHTML = `
                <div class="podium-avatar">
                    ${getAthleteInitials(athlete.name)}
                </div>
                <div class="podium-name">${escapeHtml(athlete.name)}</div>
                <div class="podium-score">${athlete.score} pts</div>
            `;
        }
    });
}

/**
 * Show empty state
 */
function showEmptyState() {
    leaderboardList.style.display = 'none';
    podiumSection.classList.add('hidden');
    emptyLeaderboard.classList.remove('hidden');
    totalAthletesEl.textContent = '0 Athletes';
}

/**
 * Get athlete initials for avatar
 */
function getAthleteInitials(name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
}

/**
 * Refresh leaderboard with latest data
 */
async function refreshLeaderboard() {
    console.log('Refreshing leaderboard...');
    showLoading(true);

    try {
        // Reload data
        await loadAthleteData();
        await loadRunSessions();

        // Re-render leaderboard
        renderLeaderboard();

        // Update timestamp
        updateLastUpdated();

        console.log('Leaderboard refreshed successfully');
    } catch (error) {
        console.error('Error refreshing leaderboard:', error);
    } finally {
        showLoading(false);
    }
}

/**
 * Update last updated timestamp
 */
function updateLastUpdated() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
    lastUpdatedEl.textContent = `Last Updated: ${timeString}`;
}

/**
 * Show/hide loading indicator
 */
function showLoading(show) {
    if (show) {
        loadingIndicator.classList.remove('hidden');
    } else {
        loadingIndicator.classList.add('hidden');
    }
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
