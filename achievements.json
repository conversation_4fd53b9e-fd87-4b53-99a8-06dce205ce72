[{"id": "first-points", "name": "First Steps", "description": "Score your first points in a ninja warrior class", "icon": "fas fa-baby", "category": "beginner", "requirement": {"type": "total_points", "value": 1}, "reward": {"xp": 10, "title": "Ninja Rookie"}, "rarity": "common"}, {"id": "point-collector", "name": "Point Collector", "description": "Accumulate 50 total points across all sessions", "icon": "fas fa-coins", "category": "progression", "requirement": {"type": "total_points", "value": 50}, "reward": {"xp": 25, "title": "Point Hunter"}, "rarity": "common"}, {"id": "century-club", "name": "Century Club", "description": "Reach 100 total points - welcome to the big leagues!", "icon": "fas fa-trophy", "category": "progression", "requirement": {"type": "total_points", "value": 100}, "reward": {"xp": 50, "title": "Century Warrior"}, "rarity": "uncommon"}, {"id": "speed-demon", "name": "Speed Demon", "description": "Complete 5 runs in a single session", "icon": "fas fa-bolt", "category": "performance", "requirement": {"type": "runs_per_session", "value": 5}, "reward": {"xp": 30, "title": "Lightning Fast"}, "rarity": "uncommon"}, {"id": "consistency-king", "name": "Consistency King", "description": "Attend 5 classes in a row without missing", "icon": "fas fa-calendar-check", "category": "attendance", "requirement": {"type": "consecutive_sessions", "value": 5}, "reward": {"xp": 40, "title": "Reliable Ninja"}, "rarity": "rare"}, {"id": "perfect-score", "name": "Perfectionist", "description": "Score 50+ points in a single session", "icon": "fas fa-star", "category": "performance", "requirement": {"type": "session_points", "value": 50}, "reward": {"xp": 60, "title": "Perfect Warrior"}, "rarity": "rare"}, {"id": "legend-status", "name": "Legendary Ninja", "description": "Reach 500 total points - you are a legend!", "icon": "fas fa-crown", "category": "progression", "requirement": {"type": "total_points", "value": 500}, "reward": {"xp": 100, "title": "Ninja Legend"}, "rarity": "legendary"}, {"id": "mystery-master", "name": "Mystery Master", "description": "Get 10 mystery box bonuses", "icon": "fas fa-gift", "category": "special", "requirement": {"type": "mystery_bonuses", "value": 10}, "reward": {"xp": 35, "title": "Lucky Ninja"}, "rarity": "uncommon"}, {"id": "comeback-kid", "name": "Comeback Kid", "description": "Recover from negative score to positive in same session", "icon": "fas fa-phoenix-rising", "category": "special", "requirement": {"type": "comeback", "value": 1}, "reward": {"xp": 45, "title": "Resilient Warrior"}, "rarity": "rare"}, {"id": "team-player", "name": "Team Player", "description": "Participate in 10 different class sessions", "icon": "fas fa-users", "category": "social", "requirement": {"type": "unique_sessions", "value": 10}, "reward": {"xp": 55, "title": "Class Champion"}, "rarity": "rare"}]